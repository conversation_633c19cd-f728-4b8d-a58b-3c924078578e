import axios from "axios";
import mpAdapter from "axios-miniprogram-adapter";
import clientBasic from "./client";
import loginRequest from "./login";
import { LOGIN_URL } from "./url";
import env from "@/env";

axios.defaults.adapter = mpAdapter;

// 初始化时从缓存恢复baseURL，如果没有缓存则使用默认值
try {
  const cachedUrl = uni.getStorageSync('api_base_url');
  axios.defaults.baseURL = cachedUrl || env.API_URL || '';
  console.log('[Ajax] 初始化baseURL:', axios.defaults.baseURL);
} catch (error) {
  axios.defaults.baseURL = env.API_URL || '';
  console.log('[Ajax] 使用默认baseURL:', axios.defaults.baseURL);
}

axios.defaults.timeout = 10000;
axios.defaults.withCredentials = true;
axios.defaults.validateStatus = (status) => status === 200;

// 设置默认请求头
axios.defaults.headers.common["Content-Type"] = "application/json";
/**
 * 动态更新axios的baseURL
 * @param {string} newBaseURL - 新的基础URL
 * @returns {boolean} 更新是否成功
 */
const updateBaseURL = (newBaseURL) => {
  try {
    if (!newBaseURL || typeof newBaseURL !== "string") {
      console.error("[Ajax] 无效的baseURL:", newBaseURL);
      return false;
    }

    // 设置 axios baseURL
    axios.defaults.baseURL = newBaseURL;

    // 自动缓存到本地存储
    uni.setStorageSync('api_base_url', newBaseURL);

    console.log(`[Ajax] baseURL已更新并缓存: ${newBaseURL}`);
    return true;
  } catch (error) {
    console.error("[Ajax] 更新baseURL失败:", error);
    return false;
  }
};
/**
 * 添加请求拦截器
 */
axios.interceptors.request.use(
  function (config) {
    const { url } = config;
    // 如果是登录和刷新token，添加专用token
    // 否则使用正常的 token
    if ([LOGIN_URL.LOGIN].includes(url)) {
      config.headers.Authorization = `Basic ${clientBasic}`;
    } else {
      config.headers.Authorization = `Bearer ${uni.getStorageSync("access_token")}`;
    }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

/**
 * 添加响应拦截器
 */
axios.interceptors.response.use(
  function (response) {
    if (response.request.responseType === "blob") {
      return response.data;
    }

    const { data, code, msg } = response.data;

    if (code === 2000) {
      return Promise.resolve(data);
    }

    // token失效，尝试使用refresh_token刷新token
    if (code === 4001) {
      const refreshToken = uni.getStorageSync("refresh_token");
      if (!refreshToken) {
        return Promise.reject(new Error(msg));
      }
      loginRequest.refreshToken();
    } else {
      return Promise.reject(msg);
    }
  },
  function (error) {
    return Promise.reject(error);
  }
);

export default {
  //GET
  async get(url, params) {
    return await axios.get(url, { params });
  },
  // POST
  async post(url, data = {}) {
    return await axios.post(url, data);
  },
  // PUT
  async put(url, data) {
    console.log(url, data);
    return await axios.put(url, data);
  },
  // DELETE
  async delete(url, params) {
    return await axios.delete(url, { params });
  },

  updateBaseURL,

  // 简单的系统配置方法
  setSystemURL(systemInfo) {
    if (!systemInfo || !systemInfo.env || !systemInfo.url) {
      console.error('[Ajax] 系统信息不完整');
      return false;
    }

    const baseUrl = env[systemInfo.env];
    if (!baseUrl) {
      console.error(`[Ajax] 环境变量 ${systemInfo.env} 未配置`);
      return false;
    }

    const fullUrl = baseUrl.replace(/\/+$/, '') + (systemInfo.url.startsWith('/') ? systemInfo.url : `/${systemInfo.url}`);
    return updateBaseURL(fullUrl);
  }
};
