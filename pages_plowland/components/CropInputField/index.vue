<template>
	<view class="gt-crop-input-field" v-show="!hide">
		<u-form-item :label="label" :prop="field" :required="required" labelWidth="auto">
			<view class="gt-crop-input-table">
				<view class="gt-crop-input-th">
					<view>作物</view>
					<view>面积</view>
				</view>
				<view class="gt-crop-input-td" v-for="item in list">
					<view>{{item.name}}</view>
					<u--input v-model="item.value" inputAlign="right" type="number" :placeholder="placeholder" :suffixIcon="unit" border="surround" clearable @change="changeHandler"
						:disabled="readonly" suffixIconStyle="fontSize: 26rpx"></u--input>
				</view>
			</view>
		</u-form-item>
	</view>
</template>

<script>
	import { isArray } from 'lodash'
	import BaseField from '@/components/form/BaseField'

	export default {

		name: 'CropInputField',

		mixins: [BaseField],
		data() {
			return {
				crop_select: [],
				crop_others: [],
				list: [],
			}
		},
		computed: {
			hide() {
				return this.options.hide === true
			}
		},
		watch: {
			crop_select() {
				this.formatValue()
			},
			crop_others() {
				this.formatValue()
			}
		},
		mounted() {
			this.bus.$on(this.$events.form.FIELD_CHANGE, this.fieldChangeHandler)
			this.initData()
			console.log(this.formData);
		},
		methods: {
			// 初始化数据
			initData() {
				let val = this.formData.备注
				let crop = this.formData.种植作物
				let other = this.formData.其它作物
				if (!crop || crop.length == 0) return
				if (crop.length > 0) {
					let has_other = crop.findIndex(item => item == '其它')
					if (has_other == -1) {
						this.crop_others = []
					}
				}
				this.crop_select = crop.filter(item => item != '其它')
				if (other) this.crop_others = this.processString(other)
				let ary = [...this.crop_select, ...this.crop_others]
				val = this.processString(val)
				ary.forEach(item => {
					let index = val.findIndex(items => {
						let index = items.indexOf(item)
						if (index > -1) return items
					})
					if (index == -1) {
						this.list.push({
							name: item,
							value: '',
						})
					} else {
						let value = val[index].replace(item, '').replace('亩', '')
						this.list.push({
							name: item,
							value,
						})
					}
				})
				this.list = this.list.filter(item => {
					return ary.includes(item.name)
				})
				this.formatValue()
			},
			fieldChangeHandler(data) {
				if (data.field == '种植作物') {
					if (isArray(data.value) && data.value.length > 0) {
						let has_other = data.value.findIndex(item => item == '其它')
						if (has_other == -1) {
							this.crop_others = []
						}
						this.crop_select = data.value.filter(item => item != '其它')
					}
				}
				if (data.field == '其它作物') {
					this.crop_others = this.processString(data.value)
				}
				let ary = [...this.crop_select, ...this.crop_others]
				ary.forEach(item => {
					let index = this.list.findIndex(items => items.name == item)
					if (index == -1) {
						this.list.push({
							name: item,
							value: '',
						})
					}
				})
				this.list = this.list.filter(item => {
					return ary.includes(item.name)
				})
			},
			processString(val) {
				let result = val.replace(/，/g, ',').replace(/\s/g, "")
				let array = result.split(',');
				array = array.filter(item => item !== '');
				return array;
			},
			formatValue() {
				let result = null
				let isNull = this.handleIsNull()
				if (!isNull) {
					result = this.list.map(item => {
						return `${item.name}${item.value}${this.unit}`
					})
					result = result.join(',')
				}
				console.log("执行赋值", result);
				this.setValue(result, false)
			},
			handleIsNull() {
				let isNull = false
				this.list.forEach(item => {
					if (!item.value) isNull = true
				})
				return isNull
			},
			setValue(value, silently = true) {
				this.value = this.genPlaceholderValue(value)
				if (!silently) this.updateFormData()
			},

			changeHandler() {
				this.formatValue()
			}
		}
	}
</script>

<style lang="less" scoped>
	.gt-crop-input-field {
		width: 100%;
	}

	.gt-crop-input-table {
		width: 100%;
	}

	.gt-crop-input-th,
	.gt-crop-input-td {
		display: grid;
		align-items: center;
		grid-template-columns: 35% 65%;
		text-align: center;
		margin-bottom: 10rpx;
	}
</style>