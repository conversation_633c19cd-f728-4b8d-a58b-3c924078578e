<template>
	<u-popup :show="value" mode="center" @close="close" closeable :closeOnClickOverlay="false" :customStyle="customPopStyle">
		<view class="form-content">
			<GTForm ref="gtForm" v-if="formOptions" :options="formOptions" @submit="submit"></GTForm>
		</view>
		<view class="btn-list" v-if="!readonly&&formOptions">
			<u-button text="立即提交" type="primary" @click="commit" throttleTime="1000"></u-button>
		</view>
	</u-popup>
</template>

<script>
	import _ from 'lodash'
	export default {
		props: {
			value: {
				type: Boolean,
				default: false,
			},
			formName: {
				type: String,
			},
			data: {
				type: Object,
				default: () => {}
			},
			readonly: {
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				formOptions: {
					formUid: null,
					formDef: null,
					formRecordData: null,
					readonly: false,
				},
				formDef: {}
			}
		},
		computed: {
			customPopStyle() {
				return {
					width: '500rpx',
					padding: '40rpx',
					'border-radius': '12rpx',
				}
			},
		},
		watch: {
			value(newVal) {
				if (newVal) {
					this.formOptions = null
					this.initOptions()
				}
			},
		},
		mounted() {
			this.init()
			this.debouncedSubmit = _.throttle(this.handleSubmit, 1000);
		},
		methods: {
			async init() {
				try {
					this.formDef[this.formName] = await this.$apis.formDef.getFormDef(this.formName);
				} catch (error) {
					console.error(error);
				}
			},
			async initOptions() {
				if (!this.formDef[this.formName]) await this.init()
				console.log("回调数据", this.data);
				let formUid = this.formName
				let formRecordData = this.data ? this.$utils.form.toFrontFormData(this.data) : null
				let readonly = this.readonly;
				let formDef = this.formDef[this.formName]
				console.log("对象", {
					formUid,
					formRecordData,
					readonly,
					formDef
				});
				this.formOptions = {
					formUid,
					formRecordData,
					readonly,
					formDef
				}
			},
			commit() {
				this.$refs.gtForm.submit();
			},
			handleSubmit(data) {
				// 处理提交的逻辑
				this.$emit('submit', data);
			},
			submit(data) {
				this.debouncedSubmit(data);
			},
			close() {
				this.$emit('input', false)
				this.$emit('close')
			},
		}
	}
</script>

<style scoped lang="scss">
	.form-content {
		width: 100%;
		max-height: 50vh;
		overflow: scroll;

		/deep/ .u-form-item__body__left {
			width: auto !important;
		}
	}
</style>