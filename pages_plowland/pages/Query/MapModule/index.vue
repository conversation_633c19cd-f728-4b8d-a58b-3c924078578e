<template>
	<view class="content">
		<u-navbar title="施工地图" :bgColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" @leftClick="navigateBack">
			<view slot="right" class="area-text" :style="{ fontSize: areaValue.length > 5 ? '24rpx' : '28rpx' }" @click="areaSelectVisible = true">切换[{{ areaValue }}]</view>
		</u-navbar>
		<view :style="contentStyle">
			<view class="search-box">
				<picker mode="selector" :range="searchType" range-key="label" @change="changeSearchType">
					<view class="search-type">
						<view class="text">{{searchTypeValue}}</view>
						<u-icon name="arrow-down" size="15"></u-icon>
					</view>
				</picker>
				<u-search :placeholder="searchPlaceholder" bgColor="#fff" v-model="searchValue" @custom="searchPointCode" @search="searchPointCode"></u-search>
			</view>
			<Map ref="map" :point="pointList" :config="mapConfig" @point-click="pointClick" @getSelectPoint="getSelectPoint" @moveEnd="refreshPoint" @mapLoaded="mapLoaded = true"
				@pointLoaded="pointLoaded = true" @pointLoading="pointLoaded = false" @zoomChange="zoomChange"></Map>
			<view class="check-all-container" v-show="selectPointList.length > 1">
				<view class="select-count">已选择{{ selectPointList.length }}个地块</view>
				<view class="btn-list">
					<u-button :plain="true" type="info" size="mini" text="取消选择" @click="resetSelectPointList">
					</u-button>
					<u-button type="primary" size="mini" text="措施记录" @click="showFillPop('cs')">
					</u-button>
					<u-button type="primary" size="mini" text="ph测量" @click="showFillPop('ph')">
					</u-button>
				</view>
			</view>
		</view>

		<AreaPicker v-model="areaSelectVisible" :mustSelect="mustSelect" @confirm="confirDistrictSelct"></AreaPicker>

		<!-- 点位详情弹窗 -->
		<u-popup :show="pointInfoVisible" @close="pointInfoClose" closeable :overlay="false">
			<view class="point-container" v-if="selectPointInfo">
				<view class="point-item-container">
					<!-- 推荐措施内容 -->
					<view class="point-item">
						<view class="point-item__title">地块信息</view>
						<view class="point-item__text" @click="copyPointCode(selectPointInfo.地块编码)">地块编码：{{ selectPointInfo.地块编码 }}</view>
						<view class="point-item__text">地址：{{ addressText }}</view>
					</view>
					<view class="point-item">
						<view class="point-item__title">推荐措施</view>
						<template v-if="selectPointInfo.measure">
							<view class="point-item__text">ph值范围{{ selectPointInfo.measure.范围最小值 }}～{{ selectPointInfo.measure.范围最大值 }}</view>
							<view class="point-item__box">
								<view class="point-item__block" v-for="(item,index) in measureList">
									<view class="point-item__label">推荐措施{{index+1}}</view>
									<view class="point-item__content">{{item}}</view>
								</view>
							</view>
						</template>
						<template v-else>
							<u-empty mode="data" text="暂无推荐措施"></u-empty>
						</template>

					</view>
				</view>
				<view class="btn-list">
					<u-button type="primary" :plain="true" text="导航" @click="navigationMap()"></u-button>
					<template>
						<u-button type="primary" text="措施记录" @click="showFillPop('cs')"></u-button>
						<u-button type="primary" text="ph测量" @click="showFillPop('ph')"></u-button>
					</template>
				</view>
			</view>
		</u-popup>
		<!-- 填报弹窗 -->
		<form-popup ref="formPopup" v-model="formInputVisible" :formName="formInputName" @submit="submit"></form-popup>
	</view>
</template>
<script>
	import BasePage from "@/pages/BasePage";
	import Map from "@/pages_plowland/components/Map";
	import AreaPicker from "@/pages_plowland/components/AreaPicker/index";
	import FormPopup from "@/pages_plowland/components/FormPopup/index";
	import Navigator from "@/tools/Navigation.js";
	import { MAP_CONFIG, PH_LEVEL } from "./config";

	const TASK_FORM_NAME = "任务表";

	export default {
		mixins: [BasePage],
		props: {},
		components: {
			Map,
			AreaPicker,
			FormPopup,
		},
		data() {
			return {
				phLevel: PH_LEVEL,
				userInfo: {},
				// 搜索
				searchType: [
					{ label: "地块编码", placeholder: "请输入地块编码" },
					{ label: "ph等级", placeholder: "请输入ph等级1～7" },
					{ label: "ph值", placeholder: "请输入ph值0～14" }
				],
				searchTypeValue: "地块编码",
				searchPlaceholder: "请输入地块编码",
				searchValue: "",
				// 地图相关
				list: [],
				pointList: [],
				selectPointList: [],
				pointLoaded: false,
				mapLoaded: false,
				mapZoom: 0,
				boundsInfo: null,
				fcode: null,
				// 行政区
				areaSelectVisible: false,
				areaValue: "",
				// 表单
				formInputVisible: false,
				formRecordData: null,
				formInputName: "",
				// 页面信息数据
				selectPointInfo: null,
				pointInfoVisible: false,
				mustSelect: false,
			};
		},
		watch: {
			fcode(newVal) {
				this.searchValue = null;
			},
		},
		computed: {
			measureList() {
				let ary = []
				for (let i = 1; i <= 8; i++) {
					let value = this.selectPointInfo.measure[`推荐措施_${i}`]
					if (value) ary.push(value)
				}
				console.log("数据：", ary);
				return ary
			},
			addressText() {
				return `${this.selectPointInfo.fname}${this.selectPointInfo.tname}${this.selectPointInfo.vname}`;
			},
			mapConfig() {
				if (!this.userInfo || !this.fcode) return;
				const result = {
					layers: MAP_CONFIG.layers,
					point: MAP_CONFIG.point,
					sources: MAP_CONFIG.sources(this.userInfo.ccode, this.fcode),
				};
				return result;
			},
			contentStyle() {
				const system = uni.getSystemInfoSync();
				return {
					position: "fixed",
					width: "100vw",
					zIndex: 1,
					top: `${system.statusBarHeight + 88}px`,
					bottom: `${system.safeAreaInsets.bottom + 50}px`,
				};
			},
		},
		async mounted() {
			this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
			// 获取任务
			await this.getCurrentTask();

			await this.getFarmerFillData();

			uni.$off("point-fly");
			uni.$on("point-fly", (data) => {
				if (this.mapLoaded) {
					this.flyToMap(data.中心经度, data.中心纬度, 18);
				}
				const interval = setInterval(() => {
					if (this.pointLoaded) {
						if (data.地块_id) data._id = data.地块_id;
						this.$refs.map.pointClick(data);
						clearInterval(interval);
					}
				}, 200);
			});
		},

		methods: {
			async getMeasureInfo(num) {
				let params = {
					filter: ["=", "措施分类编码", num],
				}
				let { list } = await this.$apis.formData.getFormRecords('措施字典表', params)
				return list[0]
			},
			// 更改搜索选项
			changeSearchType(e) {
				let index = e.detail.value
				this.searchTypeValue = this.searchType[index].label
				this.searchPlaceholder = this.searchType[index].placeholder
			},
			// 获取该农户填报的数据
			async getFarmerFillData() {
				this.ph_formRes = await this.$apis.formData.getFormRecords(
					"地块ph测量记录表", {
						filter: ["=", "create_by", this.userInfo.user_id],
					}
				);
				this.cs_formRes = await this.$apis.formData.getFormRecords(
					"措施落地佐证记录表", {
						filter: ["=", "create_by", this.userInfo.user_id],
					}
				);
			},
			async searchPointCode() {
				let info = null
				switch (this.searchTypeValue) {
					case "地块编码":
						const result = await this.$apis.custom.getFormRecords(this.fcode, '地块属性表', { filter: ["=", "地块编码", this.searchValue] });
						if (result.list.length == 0) return;
						uni.$emit("point-fly", result.list[0]);
						break;
					case "ph等级":
						info = this.phLevel.find(item => item.level == this.searchValue)
						break;
					case "ph值":
						info = this.phLevel.find(item => item.min < Number(this.searchValue) && Number(item.max) > Number(this.searchValue))
						break;
				}
				if (info) {
					this.joinsParams = [{
						"type": "inner",
						"table": "措施字典表",
						"joinedTable": `地块属性表_${this.fcode}`,
						filter: ["=", "ph等级", info.level.toString()],
						"multiJoinFields": [{
							"joinedField": "措施分类编码",
							"field": "措施分类编码"
						}]
					}]
				} else {
					this.joinsParams = null
				}
				if (this.searchTypeValue !== '地块编码') this.getSearchFormData()
			},
			//  取消框选点位
			resetSelectPointList() {
				this.$refs.map.resetPointList();
				this.selectPointList = [];
			},
			// 刷新数据
			async reloadData() {
				this.resetSelectPointList();
				await this.getFarmerFillData();
				await this.getSearchFormData();
			},
			async submit(data) {
				if (data.现场照片.length < 2) {
					uni.showToast({
						title: "至少上传2张照片",
						icon: "none"
					})
					return
				}
				try {
					const dataList = [];
					this.selectPointList.forEach((item) => {
						let info = this.projectInfoList.find(items => items.地块_id === item._id)
						const obj = {
							...data,
							地块编码: item.地块编码,
							项目编码: info ? info.项目_id : null,
						};
						dataList.push(obj);
					});
					await this.$apis.formData.batchAddFormData(
						this.formInputName,
						dataList
					);
					this.formInputVisible = false;
					this.pointInfoClose();
					this.showSuccess("填报成功");
					this.reloadData();
				} catch (error) {
					console.log(error);
					this.showError();
				}
			},
			copyPointCode(data) {
				uni.setClipboardData({
					data,
					success() {
						uni.showToast({
							title: "复制成功!",
							icon: "success",
						});
					},
				});
			},
			pointInfoClose() {
				this.pointInfoVisible = false;
				this.initPointInfo();
			},
			initPointInfo() {
				this.selectPointInfo = null;
				this.$refs.map.resetPointList();
				this.selectPointList = [];
			},
			navigateBack() {
				uni.navigateBack();
			},
			//  行政区划切换
			confirDistrictSelct(e) {
				// 判断编码并且查找镇级中心经纬度并且移至
				if (e.data.value.length >= 9) {
					this.queryLngLatFly(e.data.value.substring(0, 9));
				}
				this.areaValue = e.data.label;
				const level = e.config.valuekey;
				if (level == "fcode") this.fcode = e.data.value;
				if (level == "tcode" || level == "vcode") {
					this.areaFilter = ["=", level, e.data.value];
					this.fcode = e.data.value.slice(0, 6);
				} else {
					this.areaFilter = null;
				}
				this.areaSelectVisible = false;
				this.$refs.map.resetPointList();
			},
			async queryLngLatFly(code) {
				try {
					const filter = ["=", "tcode", code];
					const res = await this.$apis.formData.getFormRecords("admin_town", {
						filter,
					});
					const info = res.list[0];
					const min = this.$utils.proj.wgs84togcj02(info.x_min, info.y_min);
					const max = this.$utils.proj.wgs84togcj02(info.x_max, info.y_max);
					if (info.质心_x && info.质心_y) this.$refs.map.getLocation([min, max]);
				} catch (error) {
					console.log(error);
				}
			},
			flyToMap(lng, lat, zoom = 16) {
				const lnglat = this.$utils.proj.wgs84togcj02(lng, lat);
				this.$refs.map.getLocation({
					longitude: lnglat[0],
					latitude: lnglat[1],
					zoom,
				});
			},
			navigationMap(e) {
				const { 中心经度, 中心纬度, 地块编码 } = this.selectPointInfo;
				const lnglat = this.$utils.proj.wgs84togcj02(中心经度, 中心纬度);
				Navigator.navigateTo(lnglat[0], lnglat[1], `地块编码${地块编码}`);
			},
			async showFillPop(type) {
				switch (type) {
					case "ph":
						this.formInputName = "地块ph测量记录表";
						break;
					case "cs":
						this.formInputName = "措施落地佐证记录表";
						break;
				}
				// 查询选中的点所处的项目
				this.projectInfoList = await this.getPlotInfo();
				console.log("查询选中点项目", this.projectInfoList);
				this.formInputVisible = true;
				this.pointInfoVisible = true;
			},
			// 点击点
			async pointClick(e) {
				try {
					if (
						this.selectPointList.length > 1 ||
						this.selectPointList.length === 0
					) {
						this.pointInfoVisible = false;
						return;
					}
					let info = this.pointList.find(item => item.地块编码 == e.地块编码)

					if (info.措施分类编码) {
						let measure = await this.getMeasureInfo(info.措施分类编码)
						info.measure = measure
					}
					this.selectPointInfo = info;
					this.pointInfoVisible = true;
				} catch (error) {
					console.log(error);
				}
			},
			async getSelectPoint(list) {
				this.selectPointList = list;
			},
			zoomChange(e) {
				this.mapZoom = e;
			},
			refreshPoint({ bounds }) {
				const max = this.$utils.proj.gcj02towgs84(bounds._ne.lng, bounds._ne.lat);
				const min = this.$utils.proj.gcj02towgs84(bounds._sw.lng, bounds._sw.lat);
				this.boundsInfo = {
					x_max: Number(max[0]),
					y_max: Number(max[1]),
					x_min: Number(min[0]),
					y_min: Number(min[1]),
				};
				this.getSearchFormData();
			},
			//  获取任务
			async getCurrentTask() {
				const filter = ["=", "任务状态", "已启动"];
				const result = await this.$apis.formData.getFormRecords(TASK_FORM_NAME, {
					filter,
				});
				if (result.total > 0) {
					let currentTaskInfo = result.list[0];
					this.currentTaskId = currentTaskInfo._id;
					if (currentTaskInfo.时间段 == "下半年") {
						const filter1 = [
							"and",
							["=", "年份", currentTaskInfo.年份],
							["=", "时间段", "上半年"],
						];
						const lastData = await this.$apis.formData.getFormRecords(
							TASK_FORM_NAME, {
								filter: filter1,
							}
						);
						if (lastData.total > 0) this.lastTaskId = lastData.list[0]._id;
					}
				} else {
					this.currentTaskId = null;
				}
			},
			async getPlotInfo() {
				const ids = this.selectPointList.map((item) => item._id);
				const filter = ["in", "地块_id", ids];
				const result = await this.$apis.custom.getFormRecords(
					this.fcode,
					"项目地块表", {
						filter,
					}
				);
				return result.list;
			},
			//  获取过滤条件
			getParams() {
				let filter = [];
				if (this.areaFilter) filter.push(this.areaFilter);
				if (this.boundsInfo) {
					filter.push([">", "中心经度", this.boundsInfo.x_min]);
					filter.push(["<", "中心经度", this.boundsInfo.x_max]);
					filter.push([">", "中心纬度", this.boundsInfo.y_min]);
					filter.push(["<", "中心纬度", this.boundsInfo.y_max]);
				}
				if (filter.length > 1) filter.unshift('and')
				else filter = filter[0]

				let obj = {
					page: {
						pageNum: 1,
						pageSize: 1000
					},
					filter
				};

				return obj
			},
			async getSearchFormData() {
				const list = [];
				try {
					const params = this.getParams();
					let data = null
					// 判断是否需要联表查询
					if (this.joinsParams) {
						params.joins = this.joinsParams
						data = await this.$apis.formData.joinSearchFormData(`地块属性表_${this.fcode}`, params);
						let ary = []
						data.list.forEach(item => {
							let obj = {}
							Object.keys(item).forEach(key => {
								let mainKey = `地块属性表_${this.fcode}.`
								let newKey = key.replace(mainKey, "")
								if (key.includes(mainKey)) obj[newKey] = item[key]
							})
							ary.push(obj)
						})
						data.list = ary
						console.log("筛选后的数据", data);
					} else {
						data = await this.$apis.custom.getFormRecords(this.fcode, '地块属性表', params);
					}


					data.list.forEach((item) => {
						const inFormRes1 = this.ph_formRes.list.some(
							(formItem) => formItem.地块编码 === item.地块编码
						);
						const inFormRes2 = this.cs_formRes.list.some(
							(formItem) => formItem.地块编码 === item.地块编码
						);

						if (inFormRes1 || inFormRes2) {
							item.地块状态 = "已核查";
						} else {
							item.地块状态 = "未核查";
						}
						list.push(item)
					});

				} catch (error) {
					console.log(error);
				} finally {
					this.pointList = list.map((item) => {
						const lnglat = this.$utils.proj.wgs84togcj02(
							item.中心经度,
							item.中心纬度
						);
						item.longitude = lnglat[0];
						item.latitude = lnglat[1];
						return item;
					});
					this.hideLoading();
				}
			},

			locationChangeHandler(e) {
				this.$refs.map.locationChangeHandler(e);
			},
		},
	};
</script>

<style lang="scss" scoped>
	@import "@/static/styles/common.scss";

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .uicon-arrow-left {
		color: #fff !important;
	}

	/deep/ .u-checkbox {
		margin-bottom: 10rpx !important;
	}

	/deep/.u-form-item__body__left {
		width: 160rpx !important;
	}

	.area-text {
		color: #fff;
		display: flex;
		align-items: center;
	}

	.move-area {
		width: 100%;
		background-color: #fff;
		border-radius: 10px 10px 0 0;
		padding: 20rpx 0;
		box-sizing: border-box;
		overflow: hidden;
		position: fixed;
		bottom: 0;
		left: 0;
		transition: height 0.2s;
		z-index: 8;

		.move-bar {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			margin: 0 auto;
			width: 100%;
			height: 36rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.bar {
				width: 14vw;
				height: 12rpx;
				border-radius: 68rpx;
				background-color: #333;
			}
		}

		.content-top {
			padding: 0 30rpx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			height: 68rpx;
			border-bottom: 2rpx solid #f8f8f8;

			.icon {
				display: flex;
				align-items: center;
				justify-content: space-around;
				width: 150rpx;

				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}

		.list {
			transition: height 0.2s;
			position: relative;

			/deep/.u-checkbox-group--column {
				padding-bottom: 60rpx;
			}

			.card {
				box-sizing: border-box;
				display: flex;
				position: relative;
				padding: 20rpx 30rpx;
				border-bottom: 2rpx solid #f8f8f8;

				&-item {
					flex: 1;
					overflow: scroll;
					padding-right: 40rpx;
				}

				.right-icon {
					position: absolute;
					top: 50%;
					right: 10rpx;
				}

				.status {
					position: absolute;
					top: 30rpx;
					right: 80rpx;
					font-size: 24rpx;
				}

				.code {
					color: #333;
					font-size: 28rpx;
					font-weight: bold;
					margin-bottom: 10rpx;
				}

				.info {
					width: 100%;
					display: grid;

					&-item {
						height: 70rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						margin-right: 20rpx;

						.label {
							color: #333;
							font-size: 24rpx;
							font-weight: bold;
							white-space: nowrap;
						}

						.value {
							color: #333;
							font-size: 24rpx;
							white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden;
						}
					}

					.u-button {
						align-self: end;
						margin-left: auto;
						margin-right: 0;
					}
				}
			}
		}
	}

	.content {
		height: 100vh;
		overflow: hidden;
		position: relative;

		.check-all-container {
			// height: 100rpx;
			width: 100%;
			box-sizing: border-box;
			background-color: #fff;
			// padding: 0 20rpx;
			font-size: 26rpx;
			position: fixed;
			z-index: 99;
			bottom: 50px;
			padding: 30rpx;

			.btn-list {
				margin-top: 20rpx;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: flex-end;

				/deep/.u-button--mini {
					width: 140rpx;
					height: 68rpx;
					margin: 0 20rpx;
				}
			}

			.select-count {
				margin-right: 20rpx;
			}
		}

		.popup-c {
			width: 100%;
		}

		.point-container {
			padding: 30rpx;

			.point-code {
				font-weight: 700;
				margin-bottom: 30rpx;
			}

			.point-item-container {
				.point-item {
					margin-bottom: 30rpx;

					&__box {
						max-height: 600rpx;
						overflow: scroll;
					}

					&__title {
						font-size: 32rpx;
						font-weight: bold;
						margin-bottom: 16rpx;
					}

					&__text {
						font-size: 28rpx;
						margin-bottom: 8rpx;
					}

					&__block {
						border-bottom: 2rpx solid #eee;
						padding: 20rpx 0;
					}

					&__label {
						font-size: 28rpx;
						font-weight: bold;
						margin-bottom: 8rpx;
					}

					&__content {
						font-size: 24rpx;
					}
				}
			}

			.btn-list {
				display: flex;
				align-items: center;
				justify-content: space-around;

				/deep/ .u-button {
					margin-right: 20rpx;

					&:last-child {
						margin-right: 0;
					}
				}
			}
		}
	}

	.search-box {
		width: 100%;
		height: 44px;
		display: flex;
		align-items: center;
		background-color: #fff;
		position: absolute;
		top: -44px;
		left: 0;
		padding: 0 20rpx;
		box-sizing: border-box;
		z-index: 99;

		.search-type {
			display: flex;
			align-items: center;

			.text {
				width: 140rpx;
				text-align: center;
				font-size: 24rpx;
			}

		}

		.search-result {
			position: absolute;
			width: 100%;
			max-height: 30vh;
			top: 68rpx;
			left: 0;
			background-color: #fff;
			z-index: 99;
			box-shadow: 0 12rpx 12rpx rgba(0, 0, 0, 0.16);
			overflow-y: scroll;

			.block {
				width: 100%;
				padding: 20rpx 30rpx;
				border-bottom: 2rpx solid #f8f8f8;
			}
		}
	}
</style>