<template>
	<view>
		<template v-if="options">
			<MapManage :class="[tabIndex==0?'show-block':'hide-block']" :info="options" />
			<LogList :class="[tabIndex == 1 ? 'show-block' : 'hide-block']" :info="options" />
			<MonitorInfo :class="[tabIndex==2?'show-block':'hide-block']" :info="options" />
			<Train :class="[tabIndex==3?'show-block':'hide-block']" :info="options" />
		</template>
		<u-tabbar :value="tabIndex" @change="val => tabIndex = val" :fixed="true" :placeholder="false" :safeAreaInsetBottom="true">
			<u-tabbar-item text="施工地图" icon="map-fill"></u-tabbar-item>
			<u-tabbar-item text="日志列表" icon="file-text-fill"></u-tabbar-item>
			<u-tabbar-item text="监测信息" icon="list"></u-tabbar-item>
			<u-tabbar-item text="宣传培训" icon="bookmark-fill"></u-tabbar-item>
		</u-tabbar>
	</view>
</template>

<script>
	import MapManage from '@/pages_plowland/pages/Project/MapManage/index.vue'
	import LogList from '@/pages_plowland/pages/Project/LogList/index.vue'
	import MonitorInfo from '@/pages_plowland/pages/Project/MonitorInfo/index.vue'
	import Train from '@/pages_plowland/pages/Project/Train/index.vue'
	export default {
		components: { MapManage, LogList, MonitorInfo, Train },
		data() {
			return {
				tabIndex: 0,
				options: null,
			}
		},
		onLoad(options) {
			this.options = JSON.parse(options.info)
			uni.$off("changeTab")
			uni.$on('changeTab', (index) => {
				console.log("切换的tabIndex", index);
				this.tabIndex = index
			})
		},
		beforeDestroy() {
			uni.$off("refresh-project", this.refreshHandler)
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.hide-block {
		position: fixed;
		right: 9999px;
		opacity: 0;
		width: 100vw;
		height: 100vh;
		background-color: #f8f8f8;
	}

	.show-block {
		transition: opacity 0.2s;
		opacity: 1;
	}
</style>