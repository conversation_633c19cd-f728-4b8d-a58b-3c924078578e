<template>
  <view class="LogList-wrapper">
    <u-navbar
      title="日志列表"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      @leftClick="leftClickHandler"
    >
      <view
        slot="right"
        class="area-text"
        :style="{
          fontSize: regionLabel && regionLabel.length > 5 ? '24rpx' : '28rpx',
        }"
        @click="districtSelectShow = true"
        >切换[{{ regionLabel || "行政区划" }}]
      </view>
    </u-navbar>
    <view class="main">
      <view class="search-box">
        <view class="select-wrapper">
          <SelectBox
            :columns="columns"
            v-model="typeFilter"
            placeholder="技术措施"
            @change="selectChange"
          >
          </SelectBox>
        </view>
        <u-search
          placeholder="请输入地块编码"
          bgColor="#fff"
          v-model="searchValue"
          @clear="reloadData"
          @custom="reloadData"
          @search="reloadData"
        ></u-search>
      </view>

      <scroll-view
        scroll-y
        class="scroll-content"
        @scrolltolower="scrollLowerHandler"
        :style="contentStyle"
      >
        <SelfDataList
          ref="selfDataList"
          :data="dataList"
          :fields="fields"
          :titleField="titleField"
          :operations="operations"
          @edit="editHandler"
          @navigate="navigateHandler"
          @view="viewHandler"
          @delete="deleteHandler"
          :isSelect="true"
          @select="selectHandler"
        />
        <view class="loading" v-if="dataList.length > 0">
          <u-loadmore :status="loadStatus" />
        </view>
      </scroll-view>
    </view>
    <view class="check-all-container" v-show="selectData.length > 0">
      <view class="select-count">已选择{{ selectData.length }}个地块</view>
      <view class="btn-list">
        <u-button
          :plain="true"
          type="info"
          size="mini"
          text="取消选择"
          @click="resetSelectPointList"
        >
        </u-button>
        <u-button
          type="error"
          size="mini"
          text="批量删除"
          @click="deleteBatchHandler"
        >
        </u-button>
        <u-button
          type="primary"
          size="mini"
          text="批量编辑"
          @click="editBatchHandler"
        >
        </u-button>
      </view>
    </view>
    <AreaPicker
      :default="regionDefault"
      :mustSelect="mustSelect"
      class="areaPicker"
      :baseLevel="2"
      v-model="districtSelectShow"
      @confirm="confirDistrictSelct"
    ></AreaPicker>
    <u-modal
      class="delModal"
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    />
    <!-- 填报弹窗 -->
    <form-popup
      v-model="formInputVisible"
      :formName="formName"
      @submit="submit"
    ></form-popup>
  </view>
</template>

<script>
import AreaPicker from "@/pages_plowland/components/AreaPicker/index.vue";
import SelfDataList from "@/pages_plowland/components/SelfDataList/index.vue";
import SelectBox from "@/pages_plowland/components/SelectBox/index.vue";
import BaseDataList from "@/components/common/BaseDataList";
import Storage from "@/tools/Storage";
import FormPopup from "@/pages_plowland/components/FormPopup/index";

const DK_FORM = "地块属性表";
const POINT_LON_FIELD = "中心经度";
const POINT_LAT_FIELD = "中心纬度";

export default {
  name: "LogList",
  mixins: [BaseDataList],
  components: {
    SelectBox,
    SelfDataList,
    AreaPicker,
    FormPopup,
  },
  props: {
    info: {
      type: Object,
    },
  },
  created() {
    this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.mustSelect = this.userInfo.role_code.includes("city_manage");
  },
  computed: {
    contentStyle() {
      let system = uni.getSystemInfoSync();
      return {
        height: `${
          system.windowHeight - system.statusBarHeight - 44 - 50 - 60
        }px`,
      };
    },
  },
  async mounted() {
    this.projectId = this.info.id;
    if (this.info.readonly) {
      this.operations = [
        {
          key: "navigate",
          label: "定位",
        },
        {
          key: "view",
          label: "查看",
        },
      ];
    }
    await this.init();
    uni.$on("refresh-project", this.refreshHandler);
  },
  data() {
    return {
      // 搜索
      searchValue: "",
      formName: "施工日志表",
      columns: [
        [
          "全部技术措施",
          "叶面阻控",
          "品种替代",
          "水分调控",
          "优化施肥",
          "土壤PH调节",
          "其他措施",
        ],
      ],
      typeFilter: undefined,
      regionLabel: "",
      regionFilter: null,
      districtSelectShow: false,
      dataList: [],
      fields: [],
      titleField: undefined,
      operations: [
        {
          key: "navigate",
          label: "定位",
        },
        {
          key: "delete",
          label: "删除",
          type: "error",
        },
        {
          key: "edit",
          label: "编辑",
        },
        {
          key: "view",
          label: "查看",
        },
      ],
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      loadStatus: "loadmore",
      dataLoading: false,
      modalShow: false,
      delItemId: undefined,
      userInfo: undefined,
      regionDefault: null,
      mustSelect: false,

      selectData: [],
      formInputVisible: false,
    };
  },
  methods: {
    async init() {
      let res = await this.$apis.formDef.getFormDef(this.formName);
      this.fields = res.listFields;
      this.titleField = res.titleField;
    },
    leftClickHandler() {
      uni.navigateBack({ delta: 1 });
    },
    confirDistrictSelct(e) {
      const { data, config } = e;
      if (this.regionFilter && this.regionFilter.value === data.value) return;
      this.regionFilter = {
        name: config.valuekey,
        value: data.value,
      };
      this.regionLabel = data.label;
      this.districtSelectShow = false;
      this.reloadData();
    },
    async getList() {
      this.showLoading("数据加载中...");
      try {
        let joins = [
          {
            type: "left",
            table: DK_FORM,
            joinedTable: this.formName,
            multiJoinFields: [
              {
                field: "地块编码",
                joinedField: "地块编码",
              },
            ],
            outFields: ["中心经度", "中心纬度", "_id"],
          },
        ];
        let filter = [];
        filter.push(["=", "项目_id", this.projectId]);
        // 搜索条件
        if (this.typeFilter)
          filter.push(["contains", "安全利用技术措施", this.typeFilter]);
        if (this.searchValue) filter.push(["=", "地块编码", this.searchValue]);
        // 区县过滤
        if (this.regionFilter && this.regionFilter.name !== "fcode") {
          const { name, value } = this.regionFilter;
          joins[0].filter = ["=", name, value];
        }
        if (filter.length > 1) {
          filter.unshift("and");
        } else {
          filter = filter.flat(1);
        }
        let res = await this.$apis.custom.joinSearchFormData(
          this.regionFilter.value.slice(0, 6),
          this.formName,
          {
            joins,
            filter,
            page: this.page,
          }
        );
        return res.list.map((i) => {
          let obj = {};
          for (let key in i) {
            const formatKey = key.split(".")[1];
            if (key.endsWith("_id")) obj.地块_id = i[key];
            if (key.startsWith(DK_FORM) && key.endsWith("_id")) continue;
            obj[formatKey] = i[key];
          }
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async reloadData() {
      this.page.pageNum = 1;
      this.dataLoading = true;
      try {
        let list = await this.getList();
        this.dataList = list;
        if (list && list.length < this.page.pageSize)
          this.loadStatus = "nomore";
        if (list.length == this.page.pageSize) this.loadStatus = "loadmore";
        console.log("信息", list.length, this.page.pageSize);
        console.log("状态", this.loadStatus);
        console.log("数据", list);
      } catch (error) {
        console.log("加载数据出现问题", error);
        this.loadStatus = "nomore";
      } finally {
        this.hideLoading();
        this.dataLoading = false;
      }
    },
    async loadMore() {
      this.loadStatus = "loading";
      this.dataLoading = true;
      try {
        let list = await this.getList();
        this.dataList.push(...list);
        if (list && list.length < this.page.pageSize)
          this.loadStatus = "nomore";
        if (list.length == this.page.pageSize) this.loadStatus = "loadmore";
        console.log("状态", list.length, this.page.pageSize);
        console.log("数据", list);
      } catch (error) {
        console.log("加载数据发生错误：", e);
        this.loadStatus = "nomore";
      } finally {
        this.hideLoading();
        this.dataLoading = false;
      }
    },
    scrollLowerHandler() {
      if ((this.loadStatus == "nomore") | (this.dataLoading == true)) return;
      this.page.pageNum += 1;
      this.loadMore();
    },
    async editHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      try {
        let res = await this.$apis.custom.getFormRecord(
          this.regionFilter.value.slice(0, 6),
          this.formName,
          formRecordUid,
          {
            resolveDic: false,
            resolveSubItems: true,
          }
        );
        Storage.saveFormData(formRecordUid, res);
        const pageUrl = `/pages_plowland/pages/SelfFormPage/index?pcode=${this.regionFilter.value.slice(
          0,
          6
        )}&formUid=${
          this.formName
        }&formRecordUid=${formRecordUid}&cacheable=false`;
        uni.navigateTo({ url: pageUrl });
      } catch (error) {
        this.showError(error);
      }
    },
    async viewHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      let res = await this.$apis.custom.getFormRecord(
        this.regionFilter.value.slice(0, 6),
        this.formName,
        formRecordUid,
        {
          resolveDic: false,
          resolveSubItems: true,
        }
      );
      Storage.saveFormData(formRecordUid, res);
      const pageUrl = `${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formName}&formRecordUid=${formRecordUid}`;
      uni.navigateTo({ url: pageUrl });
    },
    navigateHandler(data) {
      console.log("定位信息", data);
      // let lon = data[POINT_LON_FIELD];
      // let lat = data[POINT_LAT_FIELD];
      // if (isNaN(lon) || isNaN(lat)) return;
      // Navigation.navigateTo(lon, lat);
      uni.$emit("changeTab", 0);
      uni.$emit("project-fly", data);
    },
    deleteHandler(data) {
      const formRecordUid = data._id;
      if (!formRecordUid) return;
      this.delItemId = formRecordUid;
      this.modalShow = true;
    },
    async deleteConfirmHandler() {
      try {
        this.showLoading(this.$constants.MSG.DELETING);
        if (Array.isArray(this.delItemId)) {
          await this.$apis.formData.batchDeleteFormData(
            `${this.formName}_${this.regionFilter.value.slice(0, 6)}`,
            this.delItemId
          );
          this.resetSelectPointList();
        } else {
          await this.$apis.custom.deleteFormRecord(
            this.regionFilter.value.slice(0, 6),
            this.formName,
            this.delItemId
          );
        }

        await this.reloadData();
        this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
      } catch (error) {
        this.showError(this.$constants.MSG.DELETE_FAIL);
      } finally {
        this.hideLoading();
      }
      this.delItemId = undefined;
      this.modalShow = false;
    },
    deleteCancelHandler() {
      this.delItemId = undefined;
      this.modalShow = false;
    },
    selectChange(e) {
      if (e == "全部技术措施") this.typeFilter = null;
      this.reloadData();
    },
    refreshHandler(formName) {
      console.log("formName", formName);
      if (this.formName === formName) {
        this.reloadData();
      }
    },
    resetSelectPointList() {
      this.$refs.selfDataList.resetPointList();
      this.selectData = [];
    },
    selectHandler(data) {
      this.selectData = data;
    },
    // 校验选中的数据是否可以进行编辑或删除
    checkSelectedDataPermission() {
      const selectDataSet = new Set(this.selectData);
      const selectedItems = this.dataList.filter((item) =>
        selectDataSet.has(item._id)
      );
      const { user_id } = JSON.parse(uni.getStorageSync("userInfo"));
      if (selectedItems.some((item) => item.update_by !== user_id)) {
        this.showError("只能操作该账号填报的数据！");
        return false;
      }
      return true;
    },
    deleteBatchHandler() {
      if (!this.checkSelectedDataPermission()) {
        return;
      }
      this.delItemId = this.selectData;
      this.modalShow = true;
    },

    editBatchHandler() {
      if (!this.checkSelectedDataPermission()) {
        return;
      }
      this.formInputVisible = true;
    },
    // 批量编辑提交
    async submit(data) {
      try {
        const { fcode } = JSON.parse(uni.getStorageSync("userInfo"));
        await this.$apis.custom.batchUpdateData(fcode, this.formName, {
          dataIds: this.selectData,
          data,
        });
        await this.reloadData();
        this.resetSelectPointList();
        this.showSuccess("批量编辑成功!");
        this.formInputVisible = false;
      } catch (error) {
        this.showError(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-icon__icon.uicon-arrow-left {
  color: #fff !important;
}

.area-text {
  color: #fff;
  display: flex;
  align-items: center;
}

.LogList-wrapper {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;

  .main {
    background-color: #f8f8f8;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0rpx 20rpx;

    .search-box {
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      padding: 10px 0;
      background-color: #fff;
      display: flex;
      align-items: center;
    }

    .select-wrapper {
      width: 30%;
      height: 100%;
      flex-shrink: 0;
    }

    .scroll-content {
      box-sizing: border-box;
      overflow: hidden;
    }

    .loading {
      padding-bottom: 40rpx;
    }
  }
}

.areaPicker {
  position: absolute;
}

.delModal {
  position: absolute;
}

.check-all-container {
  // height: 100rpx;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  // padding: 0 20rpx;
  font-size: 26rpx;
  position: fixed;
  z-index: 99;
  bottom: 50px;
  padding: 30rpx;

  .btn-list {
    margin-top: 20rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    /deep/.u-button--mini {
      width: 140rpx;
      height: 68rpx;
      margin: 0 20rpx;
    }
  }

  .select-count {
    margin-right: 20rpx;
  }
}
</style>
