<template>
	<view class="LogList-wrapper">
		<u-navbar title="宣传培训" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder @leftClick="leftClickHandler">
		</u-navbar>
		<view class="main">
			<scroll-view scroll-y class="scroll-content" @scrolltolower="scrollLowerHandler" :style="contentStyle">
				<SelfDataList :data="dataList" :fields="fields" :titleField="titleField" :operations="operations" @edit="editHandler" @navigate="navigateHandler" @view="viewHandler"
					@delete="deleteHandler" />
				<view class="loading" v-if="dataList.length > 0">
					<u-loadmore :status="loadStatus" />
				</view>
			</scroll-view>
		</view>
		<view class="addBtn" v-if="!info.readonly" @click="addHandler">添加</view>
		<u-modal class="delModal" :show="modalShow" :title="$constants.MSG.WARNING_TITLE" :content="$constants.MSG.WARNING_DATA_DELETE" :confirmColor="$constants.COLOR.PRIMARY_COLOR" showCancelButton
			@cancel="deleteCancelHandler" @confirm="deleteConfirmHandler" />
	</view>
</template>

<script>
	const PAGE_PARAM = {
		pageNum: 1,
		pageSize: 10,
	};
	const TASK_FORM = "安全利用项目信息表";

	import AreaPicker from '@/pages_plowland/components/AreaPicker/index.vue'
	import SelfDataList from "@/pages_plowland/components/SelfDataList/index.vue";
	import SelectBox from "@/pages_plowland/components/SelectBox/index.vue";
	import Navigation from "@/tools/Navigation";
	import BaseDataList from "@/components/common/BaseDataList";
	import Storage from "@/tools/Storage";
	export default {
		name: "LogList",
		mixins: [BaseDataList],
		components: {
			SelectBox,
			SelfDataList,
			AreaPicker
		},
		props: {
			info: {
				type: Object,
			},
		},
		computed: {
			contentStyle() {
				let system = uni.getSystemInfoSync()
				return {
					height: `${system.windowHeight-system.statusBarHeight-44-50-50}px`
				}
			},
		},
		async mounted() {
			this.projectId = this.info.id
			if (this.info.readonly) {
				this.operations = [{
					key: "view",
					label: "查看",
				}]
			}
			await this.init();
		},
		data() {
			return {
				formName: "宣传培训日志表",
				dataList: [],
				fields: [],
				titleField: undefined,
				operations: [{
						key: "view",
						label: "查看",
					},
					{
						key: "edit",
						label: "编辑",
					},
					{
						key: "delete",
						label: "删除",
						type: "error",
					},
				],
				page: PAGE_PARAM,
				loadStatus: "loadmore",
				dataLoading: false,
				modalShow: false,
				delItemId: undefined,
				userInfo: undefined,
			};
		},
		methods: {
			navigateHandler(data) {
				let lon = data[POINT_LON_FIELD];
				let lat = data[POINT_LAT_FIELD];
				if (isNaN(lon) || isNaN(lat)) return;
				Navigation.navigateTo(lon, lat);
			},
			async init() {
				let res = await this.$apis.formDef.getFormDef(this.formName);
				this.fields = res.listFields;
				this.titleField = res.titleField;
				this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
				this.reloadData();
			},
			leftClickHandler() {
				uni.navigateBack({ delta: 1, });
			},
			async getList() {
				let res = await this.$apis.formData.getFormRecords(this.formName, {
					filter: ["=", "项目_id", this.projectId],
					page: this.page,
				});
				return res.list
			},
			async reloadData() {
				this.page.pageNum = 1;
				this.dataLoading = true;
				try {
					let list = await this.getList();
					this.dataList = list;
					if (list && list.length < this.page.pageSize) {
						this.loadStatus = "nomore";
					}
				} catch (error) {
					console.log("加载数据出现问题", error);
				} finally {
					this.dataLoading = false;
				}
			},
			async loadMore() {
				this.loadStatus = "loading";
				this.dataLoading = true;
				try {
					let list = await this.getList();
					this.dataList.push(...list);
					if (list && list.length < this.page.pageSize) {
						this.loadStatus = "nomore";
					}
				} catch (error) {
					console.log("加载数据发生错误：", e);
				} finally {
					this.dataLoading = false;
				}
			},
			scrollLowerHandler() {
				if ((this.loadStatus == "nomore") | (this.dataLoading == true)) return;
				this.page.pageNum += 1;
				this.loadMore();
			},
			async addHandler() {
				const key = "add_data";
				try {
					let res = await this.$apis.formData.getFormRecord(TASK_FORM, this.projectId, {
						resolveDic: false,
						resolveSubItems: true,
					})
					const { 项目名称, 年份 } = res;
					Storage.saveFormData(key, { 项目_id: this.projectId, 项目名称, 年份 });
					uni.$off("refresh-page")
					uni.$once('refresh-page', (formName) => {
						if (this.formName !== formName) return
						this.reloadData()
					})
					const pageUrl =
						`${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${this.formName}&formRecordUid=${key}&cacheable=false`;
					uni.navigateTo({ url: pageUrl });
				} catch (error) {
					this.showError(error);
				}
			},
			async editHandler(data) {
				const formRecordUid = data._id;
				if (!formRecordUid) return;
				try {
					let res = await this.$apis.formData.getFormRecord(this.formName, formRecordUid, {
						resolveDic: false,
						resolveSubItems: true,
					})
					Storage.saveFormData(formRecordUid, res);
					uni.$off("refresh-page")
					uni.$once('refresh-page', (formName) => {
						if (this.formName !== formName) return
						this.reloadData()
					})
					const pageUrl =
						`${this.$constants.PAGE.FORM_PAGE_URL}?formUid=${this.formName}&formRecordUid=${formRecordUid}&cacheable=false`;
					uni.navigateTo({ url: pageUrl });
				} catch (error) {
					this.showError(error);
				}
			},
			async viewHandler(data) {
				const formRecordUid = data._id;
				if (!formRecordUid) return;
				let res = await this.$apis.formData.getFormRecord(this.formName, formRecordUid, {
					resolveDic: false,
					resolveSubItems: true,
				})
				Storage.saveFormData(formRecordUid, res);
				const pageUrl =
					`${this.$constants.PAGE.VIEW_PAGE_URL}?formUid=${this.formName}&formRecordUid=${formRecordUid}`;
				uni.navigateTo({ url: pageUrl });
			},
			deleteHandler(data) {
				const formRecordUid = data._id;
				if (!formRecordUid) return;
				this.delItemId = formRecordUid;
				this.modalShow = true;
			},
			async deleteConfirmHandler() {
				try {
					this.showLoading(this.$constants.MSG.DELETING);
					await this.$apis.formData.deleteFormRecord(this.formName, this.delItemId);
					await this.reloadData();
					this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
				} catch (error) {
					this.showError(this.$constants.MSG.DELETE_FAIL);
				} finally {
					this.hideLoading();
				}
				this.delItemId = undefined;
				this.modalShow = false;
			},
			deleteCancelHandler() {
				this.delItemId = undefined;
				this.modalShow = false;
			},
		},
	};
</script>

<style lang="scss" scoped>
	@import "@/static/styles/common.scss";

	/deep/ .u-navbar__content {
		.u-navbar__content__title {
			color: $gt-navbar-title-color !important;
		}
	}

	/deep/ .u-icon__icon.uicon-arrow-left {
		color: #fff !important;
	}

	.area-text {
		color: #fff;
		display: flex;
		align-items: center;
	}

	.LogList-wrapper {
		display: flex;
		flex-direction: column;
		width: 100vw;
		height: 100vh;

		.main {
			background-color: #f8f8f8;
			box-sizing: border-box;
			overflow: hidden;
			padding: 0rpx 20rpx;

			.scroll-content {
				box-sizing: border-box;
				overflow: hidden;
			}

			.loading {
				padding-bottom: 40rpx;
			}
		}
	}

	.areaPicker {
		position: absolute;
	}

	.delModal {
		position: absolute;
	}

	.addBtn {
		position: absolute;
		bottom: 50px;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 50px;
		background-color: $gt-primary-color;
		font-size: 30rpx;
		font-weight: 700;
		color: #fff;
	}
</style>