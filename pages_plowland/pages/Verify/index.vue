<template>
	<view>
		<mapCheck :class="[tabIndex==0?'show-block':'hide-block']" />
		<record :class="[tabIndex==1?'show-block':'hide-block']" />
		<schedule :class="[tabIndex==2?'show-block':'hide-block']" />
		<u-tabbar :value="tabIndex" @change="val => tabIndex = val" :fixed="true" :placeholder="false" :safeAreaInsetBottom="true">
			<u-tabbar-item text="地图核查" icon="map-fill"></u-tabbar-item>
			<u-tabbar-item text="核查记录" icon="file-text-fill"></u-tabbar-item>
			<u-tabbar-item text="核查进度" icon="hourglass-half-fill"></u-tabbar-item>
		</u-tabbar>
	</view>
</template>
<script>
	import mapCheck from '@/pages_plowland/pages/Verify/PlotInspect/index.vue'
	import record from '@/pages_plowland/pages/Verify/InspectRecord/index.vue'
	import schedule from '@/pages_plowland/pages/Verify/FillSchedule/index.vue'
	export default {
		components: { mapCheck, record, schedule },
		data() {
			return {
				tabIndex: 0
			}
		},
		onLoad() {
			uni.$off("changeTab")
			uni.$on('changeTab', (index) => {
				this.tabIndex = index
			})
		},
		methods: {}
	}
</script>

<style lang="scss" scoped>
	.hide-block {
		position: fixed;
		right: 9999px;
		opacity: 0;
		width: 100vw;
		height: 100vh;
	}

	.show-block {
		transition: opacity 0.2s;
		opacity: 1;
		width: 100vw;
		height: 100vh;
		position: relative;
	}
</style>