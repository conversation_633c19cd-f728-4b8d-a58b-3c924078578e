<template>
  <view class="content">
    <u-navbar title="核查记录" :bgColor="$constants.COLOR.PRIMARY_COLOR" :placeholder="true" @leftClick="navigateBack">
      <view slot="right" class="area-text" :style="{ fontSize: areaValue.length > 5 ? '24rpx' : '28rpx' }" @click="districtSelectShow = true"
        >切换[{{ areaValue }}]</view
      >
    </u-navbar>

    <view class="content-top">
      <view class="tab-list">
        <u-tabs
          :list="tabs"
          @click="changeTab"
          :scrollable="false"
          lineWidth="60"
          :lineColor="$constants.COLOR.PRIMARY_COLOR"
          :activeStyle="{
            color: $constants.COLOR.PRIMARY_COLOR,
            fontWeight: 'bold',
          }"
        ></u-tabs>
      </view>
      <view class="search">
        <view class="category" @click="showPicker('plot')"
          ><text>{{ plotState }}</text></view
        >
        <view class="category" @click="showPicker('ground')"
          ><text>{{ groundType }}</text></view
        >
        <u-search
          v-model="searchValue"
          :showAction="true"
          actionText="搜索"
          shape="square"
          placeholder="请输入地块编码"
          @custom="searchAddress"
          @search="searchAddress"
          @clear="searchAddress()"
        ></u-search>
      </view>
    </view>

    <view class="data-content">
      <scroll-view
        v-show="inspectState === '已核查'"
        :scroll-y="true"
        class="scroll-content"
        lower-threshold="100"
        @scrolltolower="certainScrollToLowerHandler"
      >
        <view class="data-list">
          <view class="empty-wrapper" v-if="list.certain.data.length == 0 && list.certain.loadStatus == 'nomore'">
            <u-empty text="暂无数据" marginTop="150" icon="/static/images/plowland/empty.png"></u-empty>
          </view>
          <template v-if="list.certain.data.length > 0">
            <u-checkbox-group v-model="selectIdList" placement="column" @change="checkboxChange">
              <view class="data-card" v-for="(item, index) in list.certain.data" :key="index">
                <view class="title-container">
                  <view class="container-left">
                    <view class="data-item-code" style="overflow: hidden" @click="getPointCode(item.plotCode)">地块编码：{{ item.plotCode }}</view>
                  </view>
                  <view class="container-right">{{ inspectState }}</view>
                </view>
                <view class="data-item">种植作物：{{ formatText(item) }}</view>
                <view class="data-item" v-if="item.remark">其它作物：{{ item.remark }}</view>
                <view class="data-item pictrue-container" v-if="item.pics">
                  <view>现场照片：</view>
                  <view class="album">
                    <view class="album__content">
                      <u-album :urls="concatPictureUrl(item.pics)" maxCount="3" singleMode="aspectFill" singleSize="70"></u-album>
                    </view>
                  </view>
                </view>
                <view class="data-item">核查人员：{{ item.verifyUser }}</view>
                <view class="data-item">核查日期：{{ formatDate(item.verifyTime) }}</view>
                <view class="data-item">
                  <text>地址：{{ addressText(item) }}</text>
                  <text style="color: #1677ff; margin-left: 10rpx" @click="positionPoint(item)">点击定位</text>
                </view>
                <view class="data-item" style="color: #1677ff; text-align: end; margin-top: 20rpx" @click="openSelectTask('history', item)"
                  >查看历史核查记录数据</view
                >
                <view class="btn-list">
                  <view class="btn-item" v-if="userInfo.user_id == item.createBy && !readonlyRole">
                    <u-button type="error" size="mini" text="删除" @click="operateHandler(item, 'delete')"></u-button>
                  </view>
                  <view class="btn-item" v-if="userInfo.user_id == item.createBy && !readonlyRole">
                    <u-button type="primary" size="mini" text="编辑" @click="showFillPopup('edit', item)"></u-button>
                  </view>
                  <view class="btn-item view-btn">
                    <u-button type="primary" size="mini" text="查看" @click="showFillPopup('view', item)"></u-button>
                  </view>
                </view>
              </view>
            </u-checkbox-group>
            <u-loadmore marginTop="30" marginBottom="30" :status="list.certain.loadStatus" />
            <view style="height: 80rpx"></view>
          </template>
        </view>
      </scroll-view>
      <scroll-view
        v-show="inspectState !== '已核查'"
        :scroll-y="true"
        class="scroll-content"
        lower-threshold="100"
        @scrolltolower="uncertainScrollToLowerHandler"
      >
        <view class="data-list">
          <view class="empty-wrapper" v-if="list.uncertain.data.length == 0 && list.uncertain.loadStatus == 'nomore'">
            <u-empty text="暂无数据" marginTop="150" icon="/static/images/plowland/empty.png"></u-empty>
          </view>
          <template v-if="list.uncertain.data.length > 0">
            <u-checkbox-group v-model="selectIdList" placement="column" @change="checkboxChange">
              <view class="data-card" v-for="(item, index) in list.uncertain.data" :key="index">
                <view class="title-container">
                  <view class="container-left">
                    <u-checkbox v-show="!readonlyRole" :disabled="item.disabled" v-model="item.checked" :name="item.plotId"> </u-checkbox>
                    <view class="data-item-code" style="overflow: hidden" @click="getPointCode(item.plotCode)">地块编码：{{ item.plotCode }}</view>
                  </view>
                  <view class="container-right undone">{{ inspectState }}</view>
                </view>

                <view class="data-item">
                  <text>地址：{{ addressText(item) }}</text>
                  <text style="color: #1677ff; margin-left: 10rpx" @click="positionPoint(item)">点击定位</text>
                  <view style="color: #1677ff; margin-top: 20rpx" @click="openSelectTask('history', item)">查看历史核查记录数据</view>
                </view>
                <view class="btn-list">
                  <view class="btn-item" v-show="!readonlyRole">
                    <u-button type="primary" size="mini" text="同步数据" @click="openSelectTask('sync', item)"></u-button>
                  </view>
                  <view class="btn-item view-btn" v-show="!readonlyRole">
                    <u-button type="primary" size="mini" text="填报" @click="showFillPopup('fill', item)"></u-button>
                  </view>
                </view>
              </view>
            </u-checkbox-group>
            <u-loadmore marginTop="30" marginBottom="30" :status="list.uncertain.loadStatus" />
            <view style="height: 80rpx"></view>
          </template>
        </view>
      </scroll-view>
      <view class="check-all-container" v-if="inspectState === '未核查' && list.uncertain.data.length > 0 && !readonlyRole">
        <view class="check-all">
          <u-checkbox-group v-model="selectAllState" placement="column" @change="selectAllChange">
            <u-checkbox name="all" label="全选"> </u-checkbox>
          </u-checkbox-group>
        </view>
        <view class="edit-all-btn">
          <view class="select-count">已选择{{ selectIdList.length }}个地块</view>
          <view class="edit-btn">
            <u-button type="primary" size="mini" text="同步数据" @click="openSelectTask('sync')"> </u-button>
          </view>
          <view class="edit-btn">
            <u-button type="primary" size="mini" text="批量填报" @click="showFillPopup('multiFill')"> </u-button>
          </view>
        </view>
      </view>
    </view>
    <!-- 弹窗组件 -->
    <!-- 历史任务查看 -->
    <u-popup :show="historyVisible" closeOnClickOverlay @close="historyVisible = false">
      <history-record :id="selectId" :task="taskList" :formName="formName"></history-record>
    </u-popup>
    <!-- 同步任务选择 -->
    <u-picker
      :show="taskSelectVisible"
      :columns="[taskList]"
      keyName="task_name"
      closeOnClickOverlay
      @confirm="selectTaskInfo"
      @cancel="taskSelectVisible = false"
      @close="taskSelectVisible = false"
    ></u-picker>

    <AreaPicker v-model="districtSelectShow" @confirm="confirDistrictSelct"></AreaPicker>
    <u-picker :show="pickerShow" :columns="pickerColumns" @confirm="pickerConfirm" @cancel="pickerShow = false"></u-picker>
    <!-- 删除弹窗 -->
    <u-modal
      :show="modalShow"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_DELETE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="deleteCancelHandler"
      @confirm="deleteConfirmHandler"
    >
    </u-modal>
    <form-popup
      v-model="showFillFormPopup"
      :formName="formName"
      @submit="submit"
      :data="formRecordData"
      @close="formPopupClose"
      :readonly="readonly"
    ></form-popup>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import customRequest from "@/pages_plowland/apis/custom";
import AreaPicker from "@/pages_plowland/components/AreaPicker/index";
import FormPopup from "@/pages_plowland/components/FormPopup/index";
import HistoryRecord from "../components/HistoryRecord/index";
import dayjs from "dayjs";

const FORM_NAME = "地块核查记录表";
const ADDRESS_FORM_NAME = "地块属性表";
const TASK_FORM_NAME = "任务表";
const UN_CERTAIN_FORM_NAME = "未确权地块核查记录表";
const CERTAIN_FIELDS = ["种植作物", "核查时间", "在用农户姓名", "核查人员", "备注"];
const UNCERTAIN_FIELDS = ["种植作物", "现场照片", "其它作物", "在用农户姓名", "核查时间", "核查人员", "备注"];
const FIELD_KEY = {
  cropTypeList: "种植作物",
  plotCode: "地块编码",
  cropType: "种植作物",
  remark: "备注",
  farmerName: "在用农户姓名",
  verifyUser: "核查人员",
  verifyTime: "核查时间",
  pics: "现场照片",
  otherCropType: "其它作物",
};
export default {
  mixins: [BasePage],
  components: {
    AreaPicker,
    FormPopup,
    HistoryRecord,
  },
  data() {
    return {
      fcode: null,
      formName: FORM_NAME,
      areaValue: "",
      filterDistrict: null,
      pickerColumns: [],
      plotOptions: ["QQDK", "WQQDK"],
      groundOptions: [],
      groundType: "全部",
      groundFilter: null,
      pickerType: "",
      currentTaskId: null,
      lastTaskId: null,
      taskList: [],
      userInfo: {},
      inspectState: "已核查",
      searchValue: "",
      list: {
        certain: {
          data: [],
          page: {
            pageNum: 1,
            pageSize: 10,
          },
          loadStatus: "loadmore",
          searchVal: "",
          doneChangeData: false,
        },
        uncertain: {
          data: [],
          page: {
            pageNum: 1,
            pageSize: 10,
          },
          loadStatus: "loadmore",
          searchVal: "",
          doneChangeData: true,
        },
      },
      selectIdList: [],
      selectAllState: [],
      selectData: null,
      tabs: [
        {
          name: "已核查",
          state: 1,
        },
        {
          name: "未核查",
          state: 0,
        },
      ],
      currentRecordData: null,
      formRecordData: null,
      plotState: "QQDK",
      readonly: false,
      // 弹窗
      showFillFormPopup: false,
      modalShow: false,
      taskSelectVisible: false,
      districtSelectShow: false,
      pickerShow: false,
      historyVisible: false,
    };
  },
  computed: {
    selectId() {
      if (!this.selectData) return null;
      return this.selectData.plotId;
    },
    readonlyRole() {
      if (!this.userInfo.role_code) return true;
      return !this.userInfo.role_code.includes("check_manage");
    },
  },
  async created() {
    uni.$off("refresh-list");
    uni.$on("refresh-list", () => {
      this.reloadData();
    });
    this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
    this.areaValue = this.userInfo.fname ? this.userInfo.fname : "";
    this.fcode = this.userInfo.fcode;
    this.getFilterDicts();
    await this.getCurrentTask();
    await this.reloadData();
  },
  methods: {
    // 打开任务选择弹窗
    openSelectTask(type, data) {
      this.selectData = data;
      console.log(data);
      switch (type) {
        case "history": // 查看历年数据
          this.historyVisible = true;
          break;
        case "sync": // 同步数据
          this.taskSelectVisible = true;
          break;
        default:
          break;
      }
    },
    // 选择任务信息
    async selectTaskInfo(e) {
      try {
        const task = e.value[0];
        uni.showLoading({
          title: "数据处理中...",
          mask: true,
        });
        await this.syncLastRecord(task);
      } catch (err) {
        console.log(err);
        uni.showToast({
          title: "同步数据出错!",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
        this.taskSelectVisible = false;
      }
    },
    // 同步数据
    async syncLastRecord(task) {
      if (this.requestLoad) return;
      this.requestLoad = true;
      try {
        let ids = [];
        if (!this.selectData) {
          if (this.selectIdList.length > 200) {
            uni.showToast({
              title: "单次至多只能填报200个点位，请重新选择",
              icon: "none",
            });
            return;
          }
          if (this.selectIdList.length == 0) {
            uni.showToast({
              title: "请选择需要填报的点位",
              icon: "none",
            });
            return;
          }
          ids = this.selectIdList;
        } else {
          ids = [this.selectData.plotId];
        }
        const params = {
          oldTaskId: task._id,
          newTaskId: this.currentTaskId,
          oldInfoIds: ids,
          plotType: this.plotState,
        };
        const res = await customRequest.synchroInfo(this.fcode, params);
        this.hideLoading();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS, () => {
          uni.$emit("refresh-list");
          this.reloadData();
        });
      } catch (error) {
        this.showError(error || "同步失败，请稍后重试");
        console.log(error);
      } finally {
        this.requestLoad = false;
      }
    },
    //  获取任务
    async getCurrentTask() {
      const { list: taskData } = await this.$apis.formData.getFormRecords(TASK_FORM_NAME);
      if (taskData.length !== 0) {
        const currentTaskInfo = taskData.find((item) => item.任务状态 === "已启动");
        this.currentTaskId = currentTaskInfo._id;
        let lastTask = null;
        if (currentTaskInfo.时间段 == "下半年") lastTask = taskData.find((item) => item.年份 == currentTaskInfo.年份 && item.时间段 == "上半年");

        this.taskList = taskData
          .map((item) => {
            item.task_name = `${item.年份}${item.时间段}${item.任务名称}`;
            return item;
          })
          .filter((item) => item.任务状态 !== "已启动");

        if (lastTask) this.lastTaskId = lastTask._id;
      } else {
        this.currentTaskId = null;
        this.lastTaskId = null;
      }
    },

    navigateBack() {
      uni.navigateBack();
    },
    async getFilterDicts() {
      const data = await this.$apis.formDef.getFormDef(ADDRESS_FORM_NAME);
      const ary = data.dicts.find((item) => item.name == "土地利用类型");
      ary.items.unshift("全部");
      this.groundOptions = ary.items;
    },
    showPicker(type) {
      this.pickerType = type;
      switch (type) {
        case "plot":
          this.pickerColumns = [this.plotOptions];
          break;
        case "ground":
          this.pickerColumns = [this.groundOptions];
          break;
        default:
          break;
      }
      this.pickerShow = true;
    },
    // 地址拼接
    addressText(item) {
      return `${item.fname}${item.tname}${item.vname}`;
    },
    // 种植作物格式化
    formatText(info) {
      let text = "";
      if (this.plotState == "QQDK") {
        text = info.cropType == "其它" ? info.remark : info.cropType;
      } else if (info.cropTypeList) text = info.cropTypeList.join(",");
      else text = info.cropType;
      return text;
    },
    //  行政区划切换
    async confirDistrictSelct(e) {
      this.areaValue = e.data.label;
      const level = e.config.valuekey;
      if (level == "tcode" || level == "vcode") {
        this.filterDistrict = {
          level,
          code: e.data.value,
        };
      } else {
        this.filterDistrict = null;
      }
      this.districtSelectShow = false;

      this.reloadData();
    },
    //  地块类型选择
    pickerConfirm(e) {
      const value = e.value[0];
      switch (this.pickerType) {
        case "plot":
          this.plotState = value;
          if (this.plotState === "WQQDK") this.formName = UN_CERTAIN_FORM_NAME;
          else this.formName = FORM_NAME;
          break;
        case "ground":
          if (value == "全部") {
            this.groundType = "全部";
            this.groundFilter = null;
          } else {
            this.groundType = value;
            this.groundFilter = value;
          }
          break;
        default:
          break;
      }
      this.pickerShow = false;
      this.reloadData();
    },
    getPointCode(code) {
      uni.setClipboardData({
        data: code,
        success() {
          uni.showToast({
            title: "复制成功!",
            icon: "success",
          });
        },
      });
    },
    //  触底，加载更多数据
    certainScrollToLowerHandler() {
      this.list.certain.page.pageNum += 1;
      this.getDataList();
    },
    uncertainScrollToLowerHandler() {
      this.list.uncertain.page.pageNum += 1;
      this.getDataList();
    },
    async reloadData() {
      this.list.certain.doneChangeData = true;
      this.list.uncertain.doneChangeData = true;
      const page = {
        pageNum: 1,
        pageSize: 10,
      };
      const key = this.inspectState === "已核查" ? "certain" : "uncertain";

      this.list[key].data = [];
      this.list[key].page = page;
      this.list[key].loadStatus = "loading";
      this.list[key].doneChangeData = false;
      this.currentRecordData = null;
      this.selectIdList = [];
      this.selectAllState = [];
      this.getDataList();
    },
    // 获取数据
    async getDataList() {
      if (!this.currentTaskId) return;
      if (this.dataLoading) return;
      this.dataLoading = true;
      this.showLoading("数据加载中...");
      try {
        const key = this.inspectState === "已核查" ? "certain" : "uncertain";
        const params = {
          ...this.list[key].page,
          taskId: this.currentTaskId,
          verify: this.inspectState === "已核查",
          plotType: this.plotState,
        };
        // 行政区划过滤
        if (this.filterDistrict) params[this.filterDistrict.level] = this.filterDistrict.code;
        // 搜索地块编码
        if (this.list[key].searchVal) params.plotCodes = [this.list[key].searchVal];
        if (this.groundFilter) params.landType = this.groundFilter;
        const data = await customRequest.getPlotRecord(this.fcode, params);
        data.forEach((item) => {
          this.list[key].data.push(item);
        });
        if (data.length < this.list[key].page.pageSize) {
          this.list[key].loadStatus = "nomore";
        } else {
          this.list[key].loadStatus = "loadmore";
        }
        if (data.length > 0) this.selectAllState = [];
      } catch (error) {
        console.error("error", error);
      } finally {
        this.hideLoading();
        this.dataLoading = false;
      }
    },
    //  搜索单条数据
    async searchAddress(val) {
      if (this.inspectState === "未核查") {
        this.list.uncertain.searchVal = val;
      } else if (this.inspectState === "已核查") {
        this.list.certain.searchVal = val;
      }
      this.reloadData();
    },
    //	切换tab
    changeTab(tab) {
      if (tab.name == this.inspectState) return;
      this.inspectState = tab.name;
      this.searchValue = tab.name === "未核查" ? this.list.uncertain.searchVal : this.list.certain.searchVal;
      if (
        (this.inspectState === "未核查" && this.list.uncertain.doneChangeData) ||
        (this.inspectState === "已核查" && this.list.certain.doneChangeData)
      ) {
        this.reloadData();
      }
    },
    //  卡片选择
    checkboxChange(n) {
      if (n.length == this.list.uncertain.data.length) {
        this.selectAllState = ["all"];
      } else {
        this.selectAllState = [];
      }
    },
    //  全选
    selectAllChange(n) {
      if (n.length > 0) {
        this.selectIdList = this.list.uncertain.data.map((item) => item.plotId);
      } else {
        this.selectIdList = [];
      }
    },
    formPopupClose() {
      this.formRecordData = null;
      this.readonly = false;
    },
    // 定位点
    async positionPoint(data) {
      uni.$emit("changeTab", 0);
      const list = await customRequest.getPlotInRange(this.fcode, {
        taskId: this.currentTaskId,
        plotCode: data.plotCode,
      });
      uni.$emit("flyToPoint", list[0]);
    },
    //  填报弹窗
    showFillPopup(type, data = {}) {
      this.formPopupClose();
      if (this.selectIdList.length > 200) {
        uni.showToast({
          title: "单次至多只能填报200个点位，请重新选择",
          icon: "none",
        });
        return;
      }
      if (type === "view") this.readonly = true;
      if (type === "multiFill" && this.selectIdList.length == 0) {
        uni.showToast({
          title: "请选择需要填报的点位",
          icon: "none",
        });
        return;
      }

      this.btnState = type;
      this.currentRecordData = data;
      if (type === "edit" || type === "view") {
        const fields = this.formName === "地块核查记录表" ? CERTAIN_FIELDS : UNCERTAIN_FIELDS;
        const keys = FIELD_KEY;
        const newObj = {};

        Object.keys(keys).forEach((key) => {
          const cnkey = keys[key];
          if (fields.includes(cnkey) && data[key]) {
            newObj[cnkey] = data[key];
          }
        });
        if (data.recordId) newObj._id = data.recordId;
        newObj.任务_id = this.currentTaskId;
        this.formRecordData = newObj;
      }
      this.showFillFormPopup = true;
    },
    // 查看上半年数据
    async reviewLast(info) {
      try {
        if (!this.lastTaskId) return;
        const params = {
          plotCodes: [info.plotCode],
          taskId: this.lastTaskId,
          plotType: this.plotState,
        };
        params.taskId = this.lastTaskId;
        const result = await customRequest.getPlotRecord(this.fcode, params);
        if (result.length == 0 || !result[0].recordId) {
          uni.showToast({
            title: "无上半年填报信息",
            icon: "none",
          });
          return;
        }

        const data = result[0];

        const fields = this.formName == "地块核查记录表" ? CERTAIN_FIELDS : UNCERTAIN_FIELDS;
        const keys = FIELD_KEY;
        const newObj = {};

        Object.keys(keys).forEach((key) => {
          const cnkey = keys[key];
          if (fields.includes(cnkey) && data[key]) {
            newObj[cnkey] = data[key];
          }
        });

        this.formRecordData = newObj;

        this.readonly = true;
        this.showFillFormPopup = true;
      } catch (error) {
        console.error("error", error);
      }
    },
    //  提交表单数据
    async submit(data) {
      switch (this.btnState) {
        case "edit":
          this.editFormRecord(data);
          break;
        case "fill":
          this.addFormData(data);
          break;
        case "multiFill":
          this.batchFillFormData(data);
          break;
        default:
          break;
      }
    },
    //  数据编辑
    async editFormRecord(data) {
      const formData = data;
      this.showLoading();
      try {
        await customRequest.updateFormRecord(this.fcode, this.formName, formData._id, formData);
        this.showFillFormPopup = false;
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
        uni.$emit("refresh-point");
        this.reloadData();
      } catch (error) {
        console.error("error", error);
        this.showError(error || this.$constants.MSG.DATA_SUBMIT_FAIL);
      } finally {
        this.hideLoading();
      }
    },
    //  数据新增
    async addFormData(data) {
      const { plotId } = this.currentRecordData;
      const recordData = {
        __main_form_record_uid_field__: plotId,
        任务_id: this.currentTaskId,
        ...data,
      };
      this.showLoading();
      try {
        await customRequest.addFormRecord(this.fcode, this.formName, this.$utils.form.toApiFormData(recordData));
        this.showFillFormPopup = false;
        this.hideLoading();
        uni.$emit("refresh-point");
        this.reloadData();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
      } catch (error) {
        console.error("error", error);
        this.hideLoading();
        this.showError(error || this.$constants.MSG.DATA_SUBMIT_FAIL);
      }
    },
    //  批量填报
    async batchFillFormData(data) {
      const records = [];
      this.selectIdList.forEach((id) => {
        records.push({
          __main_form_record_uid_field__: id,
          ...data,
          任务_id: this.currentTaskId,
        });
      });
      this.showLoading();
      try {
        await customRequest.batchAddFormData(this.fcode, this.formName, records);
        this.showFillFormPopup = false;
        this.hideLoading();
        uni.$emit("refresh-point");
        this.reloadData();
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
      } catch (error) {
        console.error("error", error);
        this.hideLoading();
        this.showError(error || this.$constants.MSG.DATA_SUBMIT_FAIL);
      }
    },
    //  处理获取到的时间戳
    formatDate(date) {
      return dayjs(date).format("YYYY-MM-DD");
    },
    concatPictureUrl(urlList) {
      const url = urlList.map((url) => `${process.env.OSS_ENDPOINT}/${url}`);
      return url;
    },
    //  按钮事件处理
    operateHandler(recordData, operate) {
      switch (operate) {
        case "delete":
          this.deleteHandler(recordData);
          break;
        default:
          break;
      }
    },
    //  删除数据
    deleteHandler(recordData) {
      this.currentRecordData = recordData;
      this.modalShow = true;
    },
    //  删除弹窗取消
    deleteCancelHandler() {
      this.modalShow = false;
    },
    // 弹窗确认
    async deleteConfirmHandler() {
      this.showLoading();
      try {
        await customRequest.deleteFormRecord(this.fcode, this.formName, this.currentRecordData.recordId);
        this.modalShow = false;
        this.hideLoading();
        uni.$emit("refresh-point");
        this.reloadData();
        this.showSuccess(this.$constants.MSG.DELETE_SUCCESS);
      } catch (error) {
        console.error("error", error);
        this.hideLoading();
        this.showError(error || this.$constants.MSG.DELETE_FAIL);
      }
      this.currentRecordData = null;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .uicon-arrow-left {
  color: #fff !important;
}

/deep/ .u-checkbox {
  margin-bottom: 10rpx !important;
}

/deep/.u-form-item__body__left {
  width: 160rpx !important;
}

.area-text {
  color: #fff;
  display: flex;
  align-items: center;
}

.album {
  @include flex;
  align-items: flex-start;

  &__avatar {
    background-color: $u-bg-color;
    padding: 5px;
    border-radius: 3px;
  }

  &__content {
    margin-left: 10px;
    flex: 1;
  }
}

.content {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
  box-sizing: border-box;

  /deep/.u-popup {
    flex: none;
  }

  .content-top {
    background-color: #fff;
    height: 180rpx;
    padding: 0 20rpx 20rpx;
    box-sizing: border-box;

    .search {
      display: flex;
      margin-top: 10rpx;

      .category {
        width: 120rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
      }
    }
  }

  .data-content {
    font-size: 26rpx;
    overflow-y: hidden;
    position: relative;

    /deep/.uni-scroll-view {
      padding: 0 20rpx;
      box-sizing: border-box;
    }

    .scroll-content {
      height: 100%;
      overflow-y: scroll;

      .data-list {
        .data-card {
          border-radius: 16rpx;
          background-color: #fff;
          margin-top: 20rpx;

          .title-container {
            display: flex;
            justify-content: space-between;
            border-bottom: 2rpx solid #eee;
            padding: 20rpx 30rpx;

            .container-left {
              display: flex;
            }

            .container-right {
              width: 100rpx;
              color: $gt-primary-color;
              text-align: right;
            }

            .undone {
              color: #aaa8a8;
            }
          }

          .data-item-code {
            font-weight: 700;
          }

          .data-item {
            padding: 0 30rpx;
            margin-top: 10rpx;
            color: #aaa8a8;
          }

          .pictrue-container {
            display: flex;
          }

          .btn-list {
            padding: 40rpx 20rpx 20rpx 0;
            display: flex;
            justify-content: flex-end;

            .btn-item {
              margin-right: 20rpx;

              /deep/.u-button--mini {
                width: 120rpx;
                height: 48rpx;
              }

              &:last-child {
                margin-right: 0;
              }
            }
          }
        }

        .popup-title {
          text-align: center;
        }

        .submit-btn {
          padding: 40rpx;
        }
      }
    }
  }

  .check-all-container {
    height: 100rpx;
    width: 100%;
    background-color: #fff;
    // padding: 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    position: fixed;
    bottom: 50px;

    .check-all {
      margin-left: 20rpx;
    }

    .edit-all-btn {
      display: flex;
      align-items: center;

      /deep/.u-button--mini {
        width: 120rpx;
        height: 48rpx;
      }

      .select-count {
        margin-right: 20rpx;
      }

      .edit-btn {
        margin-right: 20rpx;
      }
    }
  }
}

// }
</style>
