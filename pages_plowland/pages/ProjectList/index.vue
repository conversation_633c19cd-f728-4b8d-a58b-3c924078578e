<template>
  <view class="wrapper">
    <u-navbar
      title="项目列表"
      :bgColor="$constants.COLOR.PRIMARY_COLOR"
      placeholder
      @leftClick="leftClickHandler"
    ></u-navbar>
    <view class="main">
      <view class="select-wrapper">
        <view class="select-box" @click="selectShow = true">
          <span>{{ `${activeYear}年` }}</span>
          <u-icon name="arrow-down" color="#9ca1a8" size="20"></u-icon>
          <u-picker
            v-if="columns"
            :show="selectShow"
            :columns="columns"
            :defaultIndex="activeYearIndex"
            class="picker"
            @cancel="selectShow = false"
            @confirm="selectHandler"
          ></u-picker>
        </view>
      </view>
      <view class="list-content">
        <view class="list-box" v-if="taskList.length > 0">
          <view class="item" v-for="item in showList" :key="item._id">
            <view class="info">
              <image
                class="icon"
                src="/static/images/plowland/project-icon.svg"
                mode="aspectFill"
              ></image>
              <view class="text">{{ item.项目名称 }} {{ item.项目标段 }}</view>
            </view>
            <view class="button">
              <u-button type="primary" @click="taskClick(item)"
                >进入项目</u-button
              >
            </view>
          </view>
        </view>
        <view v-if="taskNone">
          <u-empty
            mode="data"
            marginTop="150"
            icon="http://cdn.uviewui.com/uview/empty/data.png"
            text="暂无项目"
          ></u-empty>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "projectList",
  data() {
    return {
      taskNone: false,
      activeYear: "",
      selectShow: false,
      columns: undefined,
      formName: "安全利用项目信息表",
      taskList: [],
      userInfo: undefined,
    };
  },
  computed: {
    showList() {
      return this.taskList.filter((item) => item.年份 === this.activeYear);
    },
    activeYearIndex() {
      return [this.columns[0].findIndex((item) => item === this.activeYear)];
    },
  },

  async mounted() {
    await this.getYearList();
    await this.getTaskList();
  },
  methods: {
    leftClickHandler() {
      uni.navigateBack({
        delta: 1,
      });
    },
    selectHandler(e) {
      const { value } = e;
      if (this.activeYear !== value[0]) {
        this.activeYear = value[0];
        this.getTaskList();
      }
      this.selectShow = false;
    },
    async getYearList() {
      try {
        let res = await this.$apis.formData.getFormRecords(this.formName, {
          outFields: ["年份"],
          page: {
            pageNum: 1,
            pageSize: 1000,
          },
          distinct: true,
          orders: [
            {
              field: "年份",
              direction: "desc",
            },
          ],
        });
        if (res.list.length == 0) return;
        this.userInfo = JSON.parse(uni.getStorageSync("userInfo"));
        this.activeYear = dayjs().year().toString();
        this.columns = [res.list.map((i) => i.年份)];
      } catch (error) {
        console.log("error", error);
      }
    },
    async getFilter() {
      let filter = [];
      const { role_code, ccode, fcode } = this.userInfo;
      if (role_code.includes("county_manage")) {
        filter.push(["=", "项目业主", "县级单位"]);
        filter.push(["=", "fcode", fcode]);
      } else if (role_code.includes("city_manage")) {
        filter.push(["=", "ccode", ccode]);
      } else if (role_code.includes("implementer")) {
        let { list } = await this.$apis.formData.getFormRecords("项目分配表", {
          filter: ["=", "user_id", this.userInfo.user_id],
        });
        filter.push(["in", "_id", list[0].项目列表]);
      }

      if (filter.length > 1) {
        filter.unshift("and");
      } else {
        filter = filter.flat(1);
      }
      return filter;
    },
    async getTaskList() {
      this.taskNone = false;
      try {
        let filter = await this.getFilter();
        let res = await this.$apis.formData.getFormRecords(this.formName, {
          page: {
            pageNum: 1,
            pageSize: 1000,
          },
          filter,
        });
        this.taskList = res.list;
        if (res.list.length == 0) this.taskNone = true;
      } catch (error) {
        console.log("error", error);
      }
    },
    taskClick(item) {
      const { _id } = item;
      let readonly = false;
      if (
        this.userInfo.role_code.includes("city_manage") &&
        item.项目业主 == "县级单位"
      )
        readonly = true;
      console.log(readonly, "readonly");
      let info = {
        id: _id,
        readonly,
      };
      const pageUrl = `/pages_plowland/pages/Project/index?info=${JSON.stringify(info)}`;
      uni.navigateTo({ url: pageUrl });
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/static/styles/common.scss";

/deep/ .u-navbar__content {
  .u-navbar__content__title {
    color: $gt-navbar-title-color !important;
  }
}

/deep/ .u-icon__icon.uicon-arrow-left {
  color: #fff !important;
}

.wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;

  .main {
    flex: 1;
    background-color: #f8f8f8;
    overflow: hidden;

    .select-wrapper {
      width: 100%;
      height: 115rpx;
      box-sizing: border-box;
      padding: 20rpx 20rpx 25rpx 20rpx;

      .select-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 0rpx 40rpx;
        background-color: #fff;
        border-radius: 10rpx;
        box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.12);
        color: #9ca1a8;

        .picker {
          position: absolute;
        }
      }
    }

    .list-content {
      height: calc(100% - 75rpx);
      width: 100%;
      box-sizing: border-box;
      padding: 0rpx 20rpx;
      overflow-y: auto;

      .list-box {
        box-sizing: border-box;
        padding-bottom: 20rpx;

        .item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          background-color: #fff;
          padding: 30rpx;
          width: 100%;
          border-radius: 12rpx;
          margin-bottom: 20rpx;
          box-shadow: 8rpx 8rpx 12rpx rgba(0, 0, 0, 0.06);
        }

        .info {
          display: flex;
          align-items: center;
          flex: 1;
        }

        .text {
          flex: 1;
          font-size: 28rpx;
          font-weight: bold;
          margin: 0 30rpx;
          display: -webkit-box;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .icon {
          width: 68rpx;
          height: 68rpx;
          flex-shrink: 0;
          margin-right: 20rpx;
        }

        .button {
          width: 180rpx;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
