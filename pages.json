{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [{
			"path": "app/pages/index/index"
		},
		{
			"path": "app/pages/Verify/PlotInspect/index"
		},
		{
			"path": "app/pages/Verify/InspectRecord/index"
		},
		{
			"path": "app/pages/Verify/FillSchedule/index"
		},
		{
			"path": "app/pages/UpdatePage/index"
		},
		{
			"path": "app/pages/Verify/index"
		},
		{
			"path": "app/pages/Project/index"
		},
		{
			"path": "app/pages/ProjectList/index"
		},
		{
			"path": "app/pages/SelfFormPage/index"
		},
		{
			"path": "app/pages/Query/index"
		},
		{
			"path": "pages/User/index"
		},
		{
			"path": "pages/FormList/index"
		},
		{
			"path": "pages/FormPage/index"
		},
		{
			"path": "pages/Login/index"
		},
		{
			"path": "pages/ViewPage/index"
		},
		{
			"path": "pages/ViewMultiPage/index"
		},
		{
			"path": "pages/Register/index"
		}
	],
	"tabBar": {
		"selectedColor": "#1677FF",
		"backgroundColor": "#f8f8f8",
		"borderStyle": "white",
		"iconWidth": "24px",
		"height": "50px",
		"list": [{
				"selectedIconPath": "static/images/home-active.png",
				"iconPath": "static/images/home-off.png",
				"pagePath": "app/pages/index/index",
				"text": "首页"
			},
			// {
			// 	"selectedIconPath": "static/images/check-active.png",
			// 	"iconPath": "static/images/check-off.png",
			// 	"pagePath": "app/pages/PlotInspect/index",
			// 	"text": "地块核查"
			// },
			// {
			// 	"selectedIconPath": "static/images/record-active.png",
			// 	"iconPath": "static/images/record-off.png",
			// 	"pagePath": "app/pages/InspectRecord/index",
			// 	"text": "核查记录"
			// },
			// {
			// 	"selectedIconPath": "static/images/schedule-active.png",
			// 	"iconPath": "static/images/schedule-off.png",
			// 	"pagePath": "app/pages/FillSchedule/index",
			// 	"text": "填报进度"
			// },
			{
				"selectedIconPath": "static/images/user-active.png",
				"iconPath": "static/images/user.png",
				"pagePath": "pages/User/index",
				"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationStyle": "custom",
		"navigationBarTextStyle": "white",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#ffffff",
		"app-plus": {
			"bounce": "none",
			"scrollIndicator": "none" //全局 在APP页面都不显示滚动条
		}
	}
}